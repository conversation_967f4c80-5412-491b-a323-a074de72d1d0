<template>
  <div class="table-page">
    <GiTable
      title="商务数据统计"
      row-key="salesUserId"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :isBordered="true"
      :scroll="{ x: '100%', y: '100%', minWidth: 1200 }"
      :pagination="false"
      :disabled-tools="['size', 'setting']"
      @refresh="search"
    >
      <template #toolbar-left>
        <DateRangePicker
          v-model="queryForm.dateRange"
          :show-time="false"
          :placeholder="['开始日期', '结束日期']"
          @change="onDateRangeChange"
        />
        <a-select
          v-model="queryForm.salesUserIds"
          placeholder="请选择商务人员"
          :options="userList"
          allow-clear
          allow-search
          multiple
          style="width: 200px"
          @change="search"
        />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-bottom>
        <div class="count">
          <a-card class="c-list" hoverable>
            <a-statistic title="团队总成交客户数" :value="dataSummary.totalDealsCustomers" show-group-separator />
          </a-card>
          <a-card class="c-list" hoverable>
            <a-statistic title="团队新增好友总数" :value="dataSummary.totalNewFriends" show-group-separator />
          </a-card>
          <a-card class="c-list" hoverable>
            <a-statistic title="团队整体转化率（即总成交客户数/新增好友数）" :value="dataSummary.allRate" :precision="2" show-group-separator >
              <template #suffix>%</template>
            </a-statistic>
          </a-card>
        </div>
      </template>
      <template #leadChangeRate="{ record }">
        <span class="color1">{{numberConversion(record.totalLeads, record.totalCustomerConsultations) + '%'}}</span>
      </template>
      <template #opportunityChangeRate="{ record }">
        <span class="color1">{{numberConversion(record.totalOpportunities, record.totalLeads) + '%'}}</span>
      </template>
      <template #winRate="{ record }">
        <span class="color1">{{numberConversion(record.formalCustomers, record.totalOpportunities) + '%'}}</span>
      </template>
      <template #overallChangeRate="{ record }">
        <span class="color1">{{numberConversion(record.formalCustomers, record.totalCustomerConsultations) + '%'}}</span>
      </template>
    </GiTable>
  </div>
</template>

<script setup lang="ts">
import { type SalesPerformanceStatResp, type SalesPerformanceStatQuery, listSalesPerformanceStat, salesPerformanceStatSummary } from '@/apis/biz/crm/salesPerformanceStat'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { listAllUserDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import DateRangePicker from '@/components/DateRangePicker/index.vue'
import dayjs from 'dayjs'

defineOptions({ name: 'SalesPerformanceStat' })

// 数据转换
const numberConversion = (number: number, total: number) => {
  if(number == 0 || total == 0) {
    return 0.00
  } else {
    return (number/total).toFixed(2)
  }
}

// 获取当前周的开始和结束日期
const getCurrentWeekRange = () => {
  const now = dayjs()
  // 获取当前是周几（0=周日，1=周一，...，6=周六）
  const dayOfWeek = now.day()
  // 计算到周一的偏移量
  const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek
  // 计算周一和周日，设置具体时间
  const startOfWeek = now.add(daysToMonday, 'day').startOf('day') // 周一 00:00:00
  const endOfWeek = startOfWeek.add(6, 'day').endOf('day') // 周日 23:59:59
  return [startOfWeek.format('YYYY-MM-DD HH:mm:ss'), endOfWeek.format('YYYY-MM-DD HH:mm:ss')]
}

// 获取本年度的开始和结束日期
const getCurrentYearRange = () => {
  const currentYear = new Date().getFullYear()
  const startDate = `${currentYear}-01-01 00:00:00`
  const endDate = `${currentYear}-12-31 23:59:59`
  return { startDate, endDate }
}

const { startDate: currentYearStart, endDate: currentYearEnd } = getCurrentYearRange()

const queryForm = reactive<{
  dateRange: string[]
  salesUserIds: string[]
  startDate: string
  endDate: string
}>({
  dateRange: [currentYearStart, currentYearEnd],
  salesUserIds: [],
  startDate: currentYearStart,
  endDate: currentYearEnd
})

// 用户列表
const userList = ref<LabelValueState[]>([])

/**
 * 获取用户列表（只获取商务人员）
 */
const getUserList = async () => {
  const { data } = await listAllUserDict({ roleCodePrefix: 'business_' })
  userList.value = data
}

// 初始化用户列表
getUserList()

// 数据列表
const dataList = ref<SalesPerformanceStatResp[]>([])
const loading = ref(false)
const dataSummary = reactive({
  totalDealsCustomers: 0,
  totalNewFriends: 0,
  allRate: 0
})

// 查询数据
const search = async () => {
  loading.value = true
  try {
    const query: SalesPerformanceStatQuery = {
      salesUserIds: queryForm.salesUserIds,
      startDate: queryForm.startDate,
      endDate: queryForm.endDate
    }
    const { data } = await listSalesPerformanceStat(query)
    dataList.value = data
    salesPerformanceStatSummaryGet(query)
  } catch (error) {
    console.error('查询商务业绩统计失败:', error)
    dataList.value = []
  } finally {
    loading.value = false
  }
}

// 统计
const salesPerformanceStatSummaryGet = async (params: any) => {
  console.log(111)
  const {data} = await salesPerformanceStatSummary({
    startTime: params.startDate,
    endTime: params.endDate
  })
  Object.assign(dataSummary, data, {
    allRate: parseFloat(numberConversion(data.totalDealsCustomers, data.totalNewFriends))*100
  })

  console.log(dataSummary, '____')
}

// 日期范围变化
const onDateRangeChange = (dateRange: string[]) => {
  if (dateRange && dateRange.length === 2) {
    queryForm.startDate = dateRange[0]
    queryForm.endDate = dateRange[1]
    search()
  }
}

// 表格列配置
const columns = ref<TableInstanceColumns[]>([
  { title: '商务人员', dataIndex: 'salesUserName', width: 120, fixed: 'left', align:'center' },
  {
    title: '客咨数',
    children: [
      { title: '总数', dataIndex: 'totalCustomerConsultations', width: 80, align: 'center' },
      { title: '微信', dataIndex: 'wechatConsultations', width: 80, align: 'center' },
      { title: 'TG', dataIndex: 'telegramConsultations', width: 80, align: 'center' },
      { title: '其他', dataIndex: 'otherConsultations', width: 80, align: 'center' }
    ]
  },
  {
    title: '线索数',
    children: [
      { title: '总数', dataIndex: 'totalLeads', width: 80, align: 'center' },
      { title: '跟进中', dataIndex: 'followingLeads', width: 80, align: 'center' },
      { title: '创建商机', dataIndex: 'opportunityLeads', width: 80, align: 'center' },
      { title: '无效', dataIndex: 'invalidLeads', width: 80, align: 'center' },
      { title: '线索转换率', dataIndex: 'leadChangeRate', width: 90, align: 'center',slotName: 'leadChangeRate' },
    ]
  },
  {
    title: '商机数',
    children: [
      { title: '总数', dataIndex: 'totalOpportunities', width: 80, align: 'center' },
      { title: '跟进中', dataIndex: 'followingOpportunities', width: 80, align: 'center' },
      { title: '赢单', dataIndex: 'wonOpportunities', width: 80, align: 'center' },
      { title: '流失', dataIndex: 'lostOpportunities', width: 80, align: 'center' },
      { title: '商机转化率', dataIndex: 'opportunityChangeRate', width: 90, align: 'center',slotName: 'opportunityChangeRate' },
    ]
  },
  { title: '成交客户数', dataIndex: 'formalCustomers', width: 100, align: 'center' },
  { title: '赢单率', dataIndex: 'winRate', width: 100, align: 'center',slotName: 'winRate' },
  { title: '整体转化率', dataIndex: 'overallChangeRate', width: 100, align: 'center',slotName: 'overallChangeRate' },
  {
    title: '回访数',
    children: [
      { title: '总数', dataIndex: 'totalVisits', width: 80, align: 'center' },
      { title: '待处理', dataIndex: 'pendingVisits', width: 80, align: 'center' },
      { title: '处理中', dataIndex: 'processingVisits', width: 80, align: 'center' },
      { title: '处理完成', dataIndex: 'completedVisits', width: 80, align: 'center' }
    ]
  }
])

// 重置
const reset = () => {
  const { startDate, endDate } = getCurrentYearRange()
  queryForm.salesUserIds = []
  queryForm.dateRange = [startDate, endDate]
  queryForm.startDate = startDate
  queryForm.endDate = endDate
  // 重置后自动查询数据
  search()
}

// 初始化时自动查询数据
onMounted(() => {
  search()
})
</script>

<style scoped lang="scss">
.table-page {
  height: 100%;

  :deep(.arco-table-th) {
    background-color:var(--color-bg-1);;
    font-weight: 600;
  }

  :deep(.arco-table-td) {
    text-align: center;
  }

  :deep(.arco-table-cell) {
    padding: 8px 12px;
  }
}
.count{
  width: 100%;
  display: flex;
  -webkit-display: flex;
  justify-content: space-between;
  .c-list{
    width: 30%;
    cursor: pointer;
    &:first-of-type{
      background: linear-gradient(rgb(242, 249, 254) 0%, rgb(230, 244, 254) 100%);
    }
    &:nth-of-type(2){
      background: linear-gradient(rgb(247, 247, 255) 0%, rgb(236, 236, 255) 100%);
    }
    &:last-of-type{
      background: linear-gradient(rgb(242, 249, 254) 0%, rgb(230, 244, 254) 100%);
    }
  }
}
.color1{
  color: rgb(var(--primary-6));
  font-weight: bold;
}
</style>
