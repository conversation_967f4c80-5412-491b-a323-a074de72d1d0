<template>
  <div class="table-page">
    <GiTable
      title="商务日报"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <DateRangePicker v-model="queryForm.recordDate" @change="search" />
        <a-select
          v-model="queryForm.createUser"
          :options="userList"
          placeholder="请选择创建人"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-button @click="reset">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:salesDailySummary:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:salesDailySummary:export']" @click="onExport">
          <template #icon>
            <icon-download />
          </template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:salesDailySummary:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link
            v-permission="['biz:salesDailySummary:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>
    <SalesDailySummaryAddModal ref="SalesDailySummaryAddModalRef" @save-success="search" />
  </div>
</template>

<script setup lang="ts">
import SalesDailySummaryAddModal from './SalesDailySummaryAddModal.vue'
import {
  type SalesDailySummaryQuery,
  type SalesDailySummaryResp,
  deleteSalesDailySummary,
  exportSalesDailySummary,
  listSalesDailySummary,
} from '@/apis/biz/salesDailySummary'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { isMobile } from '@/utils'
import type { LabelValueState } from '@/types/global'
import { listUserDict } from '@/apis'
import has from '@/utils/has'

defineOptions({ name: 'SalesDailySummary' })

const queryForm = reactive<SalesDailySummaryQuery>({
  recordDate: undefined,
  createUser: undefined,
  sort: ['id,desc'],
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete,
} = useTable((page) => listSalesDailySummary({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '商务', dataIndex: 'createUserString', slotName: 'createUser', align: 'center' },
  { title: '内容', dataIndex: 'content', slotName: 'content', align: 'center'},
  { title: '日期', dataIndex: 'recordDate', slotName: 'recordDate', width: 180, align: 'center' },
  { title: '创建日期', dataIndex: 'createTime', slotName: 'createTime', width: 180, align: 'center' },
  { title: '更新日期', dataIndex: 'updateTime', slotName: 'updateTime', width: 180, align: 'center' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:salesDailySummary:detail', 'biz:salesDailySummary:update', 'biz:salesDailySummary:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.recordDate = undefined
  queryForm.createUser = undefined
  search()
}

// 导出
const onExport = () => {
  useDownload(() => exportSalesDailySummary(queryForm))
}

const userList = ref<LabelValueState[]>([])

const getUserList = async () => {
  const { data } = await listUserDict()
  userList.value = data
}
getUserList()

const SalesDailySummaryAddModalRef = ref<InstanceType<typeof SalesDailySummaryAddModal>>()
// 新增
const onAdd = () => {
  SalesDailySummaryAddModalRef.value?.onAdd()
}
// 修改
const onUpdate = (record: SalesDailySummaryResp) => {
  SalesDailySummaryAddModalRef.value?.onUpdate(record.id)
}
// 删除
const onDelete = (record: SalesDailySummaryResp) => {
  return handleDelete(() => deleteSalesDailySummary(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}
</script>

<style scoped lang="scss"></style>
